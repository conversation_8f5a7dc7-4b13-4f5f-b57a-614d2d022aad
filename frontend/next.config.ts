import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    // Configure image optimization
    formats: ["image/webp", "image/avif"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Allow optimization of local images
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Disable image optimization for static export if needed
    unoptimized: false,
  },
  // Enable experimental features for better hydration
  experimental: {
    optimizePackageImports: ["lucide-react"],
  },
  // Configure Turbopack for better performance
  turbopack: {
    rules: {
      "*.svg": {
        loaders: ["@svgr/webpack"],
        as: "*.js",
      },
    },
  },
  // Ensure proper static file serving
  trailingSlash: false,
};

export default nextConfig;
