{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.19", "autoprefixer": "^10.4.21", "axios": "^1.12.2", "clsx": "^2.1.1", "framer-motion": "^12.23.22", "lucide-react": "^0.544.0", "next": "15.5.4", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.63.0", "react-hot-toast": "^2.6.0", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^3.4.17", "typescript": "^5"}}