"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, Clock, Eye, Heart, User, Tag } from "lucide-react";
import { PostListItem } from "@/types";
import { formatDate, getImageUrl } from "@/lib/utils";
import Card from "@/components/ui/Card";

interface BlogCardProps {
  post: PostListItem;
  showAuthor?: boolean;
  showStats?: boolean;
  variant?: "default" | "featured" | "compact";
}

const BlogCard: React.FC<BlogCardProps> = ({
  post,
  showAuthor = true,
  showStats = true,
  variant = "default",
}) => {
  const isCompact = variant === "compact";
  const isFeatured = variant === "featured";

  return (
    <Card
      hover
      className={`overflow-hidden ${
        isFeatured ? "lg:flex lg:items-center" : ""
      }`}
      padding="none"
    >
      {/* Featured Image */}
      {post.featured_image_url && (
        <div
          className={`relative ${
            isFeatured ? "lg:w-1/2 h-64 lg:h-80" : isCompact ? "h-48" : "h-56"
          }`}
        >
          <Image
            src={getImageUrl(post.featured_image_url)}
            alt={post.title}
            fill
            className="object-cover"
          />
          {post.is_featured && (
            <div className="absolute top-4 left-4">
              <span className="bg-accent text-secondary px-3 py-1 rounded-full text-sm font-semibold">
                Featured
              </span>
            </div>
          )}
        </div>
      )}

      {/* Content */}
      <div className={`p-6 ${isFeatured ? "lg:w-1/2" : ""}`}>
        {/* Categories */}
        {post.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {post.categories.slice(0, 2).map((category) => (
              <Link
                key={category.id}
                href={`/blog?category=${category.slug}`}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold border-2 transition-colors duration-200"
                style={{
                  borderColor: category.color,
                  color: category.color,
                }}
              >
                <Tag className="h-3 w-3 mr-1" />
                {category.name}
              </Link>
            ))}
          </div>
        )}

        {/* Title */}
        <h3
          className={`font-bold text-secondary mb-3 line-clamp-2 ${
            isFeatured ? "text-2xl" : isCompact ? "text-lg" : "text-xl"
          }`}
        >
          <Link
            href={`/blog/${post.slug}`}
            className="hover:text-primary transition-colors duration-200"
          >
            {post.title}
          </Link>
        </h3>

        {/* Excerpt */}
        {post.excerpt && !isCompact && (
          <p className="text-secondary-600 mb-4 line-clamp-3">{post.excerpt}</p>
        )}

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-secondary-500">
          {/* Author */}
          {showAuthor && (
            <div className="flex items-center space-x-2">
              {post.author.avatar_url ? (
                <Image
                  src={getImageUrl(post.author.avatar_url)}
                  alt={post.author.username}
                  width={24}
                  height={24}
                  className="rounded-full border border-neutral-200"
                />
              ) : (
                <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="h-3 w-3 text-primary" />
                </div>
              )}
              <Link
                href={`/author/${post.author.username}`}
                className="hover:text-primary transition-colors duration-200"
              >
                {post.author.first_name} {post.author.last_name}
              </Link>
            </div>
          )}

          {/* Published Date */}
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(post.published_at || post.created_at)}</span>
          </div>

          {/* Reading Time */}
          {post.reading_time_minutes && (
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{post.reading_time_minutes} min read</span>
            </div>
          )}
        </div>

        {/* Stats */}
        {showStats && (
          <div className="flex items-center space-x-4 mt-4 pt-4 border-t border-neutral-200">
            <div className="flex items-center space-x-1 text-sm text-secondary-500">
              <Eye className="h-4 w-4" />
              <span>{post.view_count}</span>
            </div>
            <div className="flex items-center space-x-1 text-sm text-secondary-500">
              <Heart className="h-4 w-4" />
              <span>{post.like_count}</span>
            </div>
          </div>
        )}

        {/* Read More Link */}
        {!isCompact && (
          <div className="mt-4">
            <Link
              href={`/blog/${post.slug}`}
              className="inline-flex items-center text-primary hover:text-accent font-medium transition-colors duration-200"
            >
              Read More
              <svg
                className="ml-2 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
        )}
      </div>
    </Card>
  );
};

export default BlogCard;
