"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Send,
} from "lucide-react";
import { apiClient } from "@/lib/api";
import { getErrorMessage } from "@/lib/utils";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import toast from "react-hot-toast";

const Footer: React.FC = () => {
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [isSubscribing, setIsSubscribing] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error("Please enter your email address");
      return;
    }

    setIsSubscribing(true);

    try {
      await apiClient.subscribeToNewsletter({
        email: email.trim(),
        first_name: firstName.trim() || undefined,
        subscription_source: "website_footer",
      });

      toast.success("Successfully subscribed to our newsletter!");
      setEmail("");
      setFirstName("");
    } catch (error) {
      toast.error(getErrorMessage(error));
    } finally {
      setIsSubscribing(false);
    }
  };

  const footerLinks = {
    blog: [
      { name: "Latest Posts", href: "/blog" },
      { name: "Homeschooling", href: "/blog?category=homeschooling" },
      { name: "Family Life", href: "/blog?category=family-life" },
      { name: "Faith & Spirituality", href: "/blog?category=faith" },
    ],
    resources: [
      { name: "Curriculum Reviews", href: "/resources/curriculum" },
      { name: "Learning Activities", href: "/resources/activities" },
      { name: "Printables", href: "/resources/printables" },
      { name: "Book Recommendations", href: "/resources/books" },
    ],
    community: [
      { name: "About Us", href: "/about" },
      { name: "Contact", href: "/contact" },
      { name: "Newsletter", href: "/newsletter" },
      { name: "Support", href: "/support" },
    ],
    legal: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
      { name: "Cookie Policy", href: "/cookies" },
    ],
  };

  const socialLinks = [
    { name: "Facebook", href: "#", icon: Facebook },
    { name: "Twitter", href: "#", icon: Twitter },
    { name: "Instagram", href: "#", icon: Instagram },
    { name: "YouTube", href: "#", icon: Youtube },
  ];

  return (
    <footer className="bg-secondary text-white">
      {/* Newsletter Section */}
      <div className="border-b border-secondary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Stay Connected</h3>
            <p className="text-secondary-200 mb-8 max-w-2xl mx-auto">
              Get the latest homeschooling tips, family activities, and
              faith-based resources delivered straight to your inbox.
            </p>

            <form
              onSubmit={handleNewsletterSubmit}
              className="max-w-md mx-auto"
            >
              <div className="space-y-4">
                <Input
                  type="text"
                  placeholder="First Name (optional)"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="bg-white text-secondary"
                />
                <div className="flex space-x-2">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="bg-white text-secondary flex-1"
                  />
                  <Button
                    type="submit"
                    variant="accent"
                    loading={isSubscribing}
                    className="px-6"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <Image
                src="/logo.png"
                alt="Belong Together Ministries"
                width={48}
                height={48}
                className="rounded-lg"
                sizes="48px"
                style={{
                  width: "48px",
                  height: "48px",
                }}
              />
              <div>
                <h3 className="text-xl font-bold">Belong Together</h3>
                <p className="text-secondary-300">Ministries</p>
              </div>
            </div>
            <p className="text-secondary-200 mb-6 max-w-md">
              Supporting families in their homeschooling journey with
              faith-based resources, practical tips, and a loving community.
            </p>

            {/* Contact Info */}
            <div className="space-y-2 text-sm text-secondary-200">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>(*************</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Serving families nationwide</span>
              </div>
            </div>
          </div>

          {/* Blog Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Blog</h4>
            <ul className="space-y-2">
              {footerLinks.blog.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-secondary-200 hover:text-accent transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Resources</h4>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-secondary-200 hover:text-accent transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Community Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Community</h4>
            <ul className="space-y-2">
              {footerLinks.community.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-secondary-200 hover:text-accent transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Links */}
        <div className="mt-12 pt-8 border-t border-secondary-600">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex space-x-4 mb-4 md:mb-0">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="p-2 rounded-lg bg-secondary-700 hover:bg-accent transition-colors duration-200"
                    aria-label={social.name}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>

            <div className="text-center md:text-right">
              <p className="text-secondary-200 text-sm">
                © {new Date().getFullYear()} Belong Together Ministries. All
                rights reserved.
              </p>
              <div className="flex flex-wrap justify-center md:justify-end space-x-4 mt-2">
                {footerLinks.legal.map((link, index) => (
                  <React.Fragment key={link.name}>
                    <Link
                      href={link.href}
                      className="text-secondary-300 hover:text-accent text-sm transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                    {index < footerLinks.legal.length - 1 && (
                      <span className="text-secondary-400">•</span>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
