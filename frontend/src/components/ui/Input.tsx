import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";
import { InputProps } from "@/types";

interface ExtendedInputProps
  extends InputProps,
    Omit<React.InputHTMLAttributes<HTMLInputElement>, "type"> {}

const Input = forwardRef<HTMLInputElement, ExtendedInputProps>(
  (
    {
      label,
      error,
      placeholder,
      type = "text",
      required = false,
      disabled = false,
      className,
      ...props
    },
    ref
  ) => {
    const inputClasses = cn(
      "w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0",
      error
        ? "border-alert-500 focus:ring-alert-500 focus:border-alert-500"
        : "border-neutral-300 focus:ring-primary-500 focus:border-primary-500",
      disabled && "bg-neutral-100 cursor-not-allowed",
      className
    );

    return (
      <div className="space-y-1">
        {label && (
          <label className="block text-sm font-semibold text-secondary">
            {label}
            {required && <span className="text-alert-500 ml-1">*</span>}
          </label>
        )}
        <input
          ref={ref}
          type={type}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={inputClasses}
          {...props}
        />
        {error && <p className="text-sm text-alert-500">{error}</p>}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
