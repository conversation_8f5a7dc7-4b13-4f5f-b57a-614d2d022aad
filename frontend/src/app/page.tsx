"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { ArrowRight, BookOpen, Users, Heart, Star } from "lucide-react";
import { PostListItem, Category } from "@/types";
import { apiClient } from "@/lib/api";
import { getErrorMessage } from "@/lib/utils";
import Layout from "@/components/layout/Layout";
import BlogCard from "@/components/blog/BlogCard";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";

export default function Home() {
  const [featuredPosts, setFeaturedPosts] = useState<PostListItem[]>([]);
  const [recentPosts, setRecentPosts] = useState<PostListItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        setIsLoading(true);

        // Fetch featured posts, recent posts, and categories in parallel
        const [featuredResponse, recentResponse, categoriesResponse] =
          await Promise.all([
            apiClient.getPosts({ featured: true, limit: 3 }),
            apiClient.getPosts({ limit: 6 }),
            apiClient.getCategories(),
          ]);

        setFeaturedPosts(featuredResponse);
        setRecentPosts(recentResponse);
        setCategories(categoriesResponse.filter((cat) => cat.is_active));
      } catch (err) {
        setError(getErrorMessage(err));
      } finally {
        setIsLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  // if (error) {
  //   return (
  //     <Layout>
  //       <div className="min-h-screen flex items-center justify-center">
  //         <Card className="max-w-md mx-auto text-center">
  //           <p className="text-alert-600 mb-4">Failed to load content</p>
  //           <Button onClick={() => window.location.reload()}>Try Again</Button>
  //         </Card>
  //       </div>
  //     </Layout>
  //   );
  // }

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-primary text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                Welcome to Belong Together Ministries
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-white/90">
                Supporting families in their homeschooling journey with
                faith-based resources, practical tips, and a loving community.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/blog">
                  <Button
                    variant="accent"
                    size="lg"
                    className="w-full sm:w-auto"
                  >
                    Explore Our Blog
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/about">
                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-primary"
                  >
                    Learn More About Us
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/hero-family.svg"
                alt="Happy homeschooling family"
                width={600}
                height={400}
                className="rounded-lg border-4 border-white/20"
                priority
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
                style={{
                  width: "100%",
                  height: "auto",
                  maxWidth: "600px",
                }}
              />
            </div>
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary mb-4">
              Why Choose Belong Together?
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              We&apos;re here to support your family&apos;s unique homeschooling
              journey with resources, community, and faith-centered guidance.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card hover className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-secondary mb-3">
                Educational Resources
              </h3>
              <p className="text-secondary-600">
                Curriculum reviews, learning activities, and practical
                homeschooling tips to help your children thrive.
              </p>
            </Card>

            <Card hover className="text-center">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-accent-700" />
              </div>
              <h3 className="text-xl font-semibold text-secondary mb-3">
                Supportive Community
              </h3>
              <p className="text-secondary-600">
                Connect with like-minded families, share experiences, and find
                encouragement in your homeschooling journey.
              </p>
            </Card>

            <Card hover className="text-center">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="h-8 w-8 text-secondary" />
              </div>
              <h3 className="text-xl font-semibold text-secondary mb-3">
                Faith-Centered Approach
              </h3>
              <p className="text-secondary-600">
                Integrate faith into your family&apos;s education with biblical
                perspectives and Christian values.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Featured Posts Section */}
      {!isLoading && featuredPosts.length > 0 && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-secondary mb-4">
                Featured Articles
              </h2>
              <p className="text-xl text-secondary-600">
                Don&apos;t miss these popular posts from our community
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {featuredPosts.map((post) => (
                <BlogCard key={post.id} post={post} variant="default" />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Posts Section */}
      {!isLoading && recentPosts.length > 0 && (
        <section className="py-16 bg-neutral-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-secondary mb-4">
                  Latest Posts
                </h2>
                <p className="text-xl text-secondary-600">
                  Stay up to date with our newest content
                </p>
              </div>
              <Link href="/blog">
                <Button variant="outline">
                  View All Posts
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {recentPosts.slice(0, 6).map((post) => (
                <BlogCard key={post.id} post={post} variant="compact" />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Categories Section */}
      {!isLoading && categories.length > 0 && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-secondary mb-4">
                Explore Topics
              </h2>
              <p className="text-xl text-secondary-600">
                Find content that matches your interests
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {categories.slice(0, 8).map((category) => (
                <Link
                  key={category.id}
                  href={`/blog?category=${category.slug}`}
                >
                  <Card hover className="text-center">
                    <div className="p-4">
                      <div
                        className="w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center"
                        style={{ backgroundColor: `${category.color}20` }}
                      >
                        <Star
                          className="h-6 w-6"
                          style={{ color: category.color }}
                        />
                      </div>
                      <h3 className="font-semibold text-secondary text-sm">
                        {category.name}
                      </h3>
                    </div>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Loading State */}
      {isLoading && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-secondary-600">Loading content...</p>
            </div>
          </div>
        </section>
      )}
    </Layout>
  );
}
