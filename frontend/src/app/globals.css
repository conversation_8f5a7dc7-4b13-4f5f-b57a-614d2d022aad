@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #9d7e3b;
  --secondary: #314345;
  --accent: #e3c563;
  --alert: #c2573f;
  --neutral: #ffffff;
}

@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    background-color: #ffffff;
    color: #192022;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: #314345;
    font-weight: 600;
    line-height: 1.2;
  }

  /* Ensure images are responsive by default */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Fix for Next.js Image hydration */
  img[data-nimg] {
    color: transparent;
  }
}

@layer components {
  /* Custom component styles - using basic CSS instead of @apply */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: color 0.2s ease-in-out;
  }

  .card {
    background-color: white;
    border-radius: 0.5rem;
    border: 2px solid #f5f5f5;
  }
}

@layer utilities {
  /* Gradient utilities */
  .gradient-primary {
    background: linear-gradient(135deg, #9d7e3b 0%, #8d7135 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #314345 0%, #2c3c3e 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, #e3c563 0%, #ccb159 100%);
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
