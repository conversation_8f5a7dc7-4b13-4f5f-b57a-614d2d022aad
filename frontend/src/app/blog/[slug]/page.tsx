"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import {
  Calendar,
  Clock,
  Eye,
  Heart,
  User,
  Tag,
  ArrowLeft,
} from "lucide-react";
import { Post } from "@/types";
import { apiClient } from "@/lib/api";
import {
  formatDate,
  getImageUrl,
  createShareUrl,
  getErrorMessage,
} from "@/lib/utils";
import { useAuthStore } from "@/store/auth";
import Layout from "@/components/layout/Layout";
import SocialShare from "@/components/blog/SocialShare";
import MetaTags from "@/components/seo/MetaTags";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import toast from "react-hot-toast";

export default function BlogPostPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [post, setPost] = useState<Post | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    const fetchPost = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const postData = await apiClient.getPost(slug);
        setPost(postData);
        setLikeCount(postData.like_count);

        // TODO: Check if user has liked/favorited this post
        // This would require additional API endpoints
      } catch (err) {
        setError(getErrorMessage(err));
      } finally {
        setIsLoading(false);
      }
    };

    if (slug) {
      fetchPost();
    }
  }, [slug]);

  const handleLike = async () => {
    if (!isAuthenticated || !post) {
      toast.error("Please sign in to like posts");
      return;
    }

    try {
      const result = await apiClient.likePost(post.id);
      setIsLiked(result.liked);
      setLikeCount(result.like_count);

      toast.success(result.liked ? "Post liked!" : "Post unliked");
    } catch (err) {
      toast.error(getErrorMessage(err));
    }
  };

  const handleFavorite = async () => {
    if (!isAuthenticated || !post) {
      toast.error("Please sign in to favorite posts");
      return;
    }

    try {
      const result = await apiClient.favoritePost(post.id);
      setIsFavorited(result.favorited);

      toast.success(
        result.favorited ? "Added to favorites!" : "Removed from favorites"
      );
    } catch (err) {
      toast.error(getErrorMessage(err));
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-secondary-600">Loading post...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !post) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <Card className="max-w-md mx-auto text-center">
            <p className="text-alert-600 mb-4">{error || "Post not found"}</p>
            <Link href="/blog">
              <Button>Back to Blog</Button>
            </Link>
          </Card>
        </div>
      </Layout>
    );
  }

  const shareUrl = createShareUrl(post.slug);

  return (
    <Layout>
      <MetaTags
        title={post.title}
        description={post.excerpt}
        type="article"
        author={
          post.author.first_name && post.author.last_name
            ? `${post.author.first_name} ${post.author.last_name}`
            : post.author.username
        }
        publishedTime={post.created_at}
        modifiedTime={post.updated_at}
        section={post.categories[0]?.name}
        tags={post.categories.map((cat) => cat.name)}
        image={getImageUrl(post.featured_image_url)}
        url={shareUrl}
      />
      {/* Back to Blog */}
      <div className="bg-neutral-50 border-b-2 border-neutral-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-secondary-600 hover:text-primary transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Blog
          </Link>
        </div>
      </div>

      <article className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <header className="mb-8">
            {/* Categories */}
            {post.categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {post.categories.map((category) => (
                  <Link
                    key={category.id}
                    href={`/blog?category=${category.slug}`}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold border-2 transition-colors duration-200"
                    style={{
                      borderColor: category.color,
                      color: category.color,
                    }}
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    {category.name}
                  </Link>
                ))}
              </div>
            )}

            {/* Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary mb-6">
              {post.title}
            </h1>

            {/* Excerpt */}
            {post.excerpt && (
              <p className="text-xl text-secondary-600 mb-6">{post.excerpt}</p>
            )}

            {/* Meta information */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-secondary-500 mb-6">
              {/* Author */}
              <div className="flex items-center space-x-2">
                {post.author.avatar_url ? (
                  <Image
                    src={getImageUrl(post.author.avatar_url)}
                    alt={post.author.username}
                    width={32}
                    height={32}
                    className="rounded-full border-2 border-neutral-200"
                  />
                ) : (
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-primary" />
                  </div>
                )}
                <Link
                  href={`/author/${post.author.username}`}
                  className="hover:text-primary transition-colors duration-200"
                >
                  {post.author.first_name} {post.author.last_name}
                </Link>
              </div>

              {/* Published Date */}
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(post.published_at || post.created_at)}</span>
              </div>

              {/* Reading Time */}
              {post.reading_time_minutes && (
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>{post.reading_time_minutes} min read</span>
                </div>
              )}

              {/* View Count */}
              <div className="flex items-center space-x-1">
                <Eye className="h-4 w-4" />
                <span>{post.view_count} views</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleLike}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg border-2 transition-colors duration-200 ${
                    isLiked
                      ? "border-primary bg-primary text-white"
                      : "border-neutral-300 text-secondary-600 hover:border-primary hover:text-primary"
                  }`}
                >
                  <Heart
                    className={`h-4 w-4 ${isLiked ? "fill-current" : ""}`}
                  />
                  <span>{likeCount}</span>
                </button>

                <button
                  onClick={handleFavorite}
                  className={`px-4 py-2 rounded-lg border-2 transition-colors duration-200 ${
                    isFavorited
                      ? "border-accent bg-accent text-secondary"
                      : "border-neutral-300 text-secondary-600 hover:border-accent hover:text-accent-700"
                  }`}
                >
                  {isFavorited ? "Favorited" : "Add to Favorites"}
                </button>
              </div>

              <SocialShare
                url={shareUrl}
                title={post.title}
                description={post.excerpt}
                variant="horizontal"
                showLabel
              />
            </div>
          </header>

          {/* Featured Image */}
          {post.featured_image_url && (
            <div className="mb-8">
              <Image
                src={getImageUrl(post.featured_image_url)}
                alt={post.title}
                width={800}
                height={400}
                className="w-full h-64 md:h-96 object-cover rounded-lg border-2 border-neutral-200"
              />
            </div>
          )}

          {/* Content */}
          <div
            className="prose prose-lg max-w-none prose-headings:text-secondary prose-a:text-primary hover:prose-a:text-accent prose-blockquote:border-l-primary"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />

          {/* Footer Actions */}
          <div className="mt-12 pt-8 border-t-2 border-neutral-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleLike}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg border-2 transition-colors duration-200 ${
                    isLiked
                      ? "border-primary bg-primary text-white"
                      : "border-neutral-300 text-secondary-600 hover:border-primary hover:text-primary"
                  }`}
                >
                  <Heart
                    className={`h-4 w-4 ${isLiked ? "fill-current" : ""}`}
                  />
                  <span>Like this post</span>
                </button>

                <button
                  onClick={handleFavorite}
                  className={`px-4 py-2 rounded-lg border-2 transition-colors duration-200 ${
                    isFavorited
                      ? "border-accent bg-accent text-secondary"
                      : "border-neutral-300 text-secondary-600 hover:border-accent hover:text-accent-700"
                  }`}
                >
                  {isFavorited ? "Remove from Favorites" : "Save for Later"}
                </button>
              </div>

              <SocialShare
                url={shareUrl}
                title={post.title}
                description={post.excerpt}
                variant="dropdown"
                showLabel
              />
            </div>
          </div>
        </div>
      </article>
    </Layout>
  );
}
